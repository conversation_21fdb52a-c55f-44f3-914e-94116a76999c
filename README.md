# 面试官"见招拆招"App

## 项目概述

这是一款基于AI的实时面试辅助移动应用，帮助技术岗位面试者通过手机听筒实时获取针对面试官提问的专业回答建议。

## 技术栈

- **前端**: uni-app + Vue 3 + Pinia
- **后端**: Go + Gin + MySQL + Redis
- **AI模型**: Google Gemini Live API
- **通信**: WebSocket + RESTful API
- **第三方服务**: 阿里云短信、微信支付、支付宝

## 项目结构

```
interviewMaster/
├── app/                    # 前端uni-app项目
├── backend/               # 后端Go项目
│   ├── main.go           # 主程序入口
│   ├── config.json       # 配置文件
│   └── config.example.json # 配置示例
├── docs/                 # 文档
├── scripts/              # 脚本文件
└── README.md
```

## 快速开始

### 后端启动

```bash
cd backend
go mod tidy
go run main.go
```

### 前端启动

```bash
cd app
npm install
npm run dev
```

## 开发进度

- [x] 项目初始化
- [ ] 后端核心架构
- [ ] 数据库设计
- [ ] 用户认证系统
- [ ] WebSocket实时通信
- [ ] Gemini API集成
- [ ] 前端页面开发
- [ ] 音频处理模块
- [ ] 支付系统集成
- [ ] 后台管理系统

## 贡献指南

请参考 [CONTRIBUTING.md](docs/CONTRIBUTING.md) 了解如何参与项目开发。

## 许可证

MIT License
