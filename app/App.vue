<template>
  <view id="app">
    <!-- 应用根组件 -->
  </view>
</template>

<script>
export default {
  name: 'App',
  onLaunch: function() {
    console.log('App Launch')
    // 应用启动时的逻辑
    this.initApp()
  },
  onShow: function() {
    console.log('App Show')
  },
  onHide: function() {
    console.log('App Hide')
  },
  methods: {
    initApp() {
      // 检查登录状态
      const token = uni.getStorageSync('token')
      if (token) {
        // 验证token有效性
        this.validateToken(token)
      }
      
      // 检查权限
      this.checkPermissions()
    },
    
    validateToken(token) {
      // TODO: 验证token
      console.log('验证token:', token)
    },
    
    checkPermissions() {
      // 检查录音权限
      uni.authorize({
        scope: 'scope.record',
        success() {
          console.log('录音权限已授权')
        },
        fail() {
          console.log('录音权限未授权')
        }
      })
    }
  }
}
</script>

<style lang="scss">
/* 全局样式 */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 通用样式 */
.container {
  padding: 20rpx;
}

.btn-primary {
  background-color: #007aff;
  color: white;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  border: none;
  font-size: 32rpx;
}

.btn-primary:active {
  background-color: #0056cc;
}

.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column {
  flex-direction: column;
}
</style>
