<template>
  <view class="login-container">
    <view class="header">
      <text class="title">面试助手</text>
      <text class="subtitle">AI助力，面试无忧</text>
    </view>
    
    <view class="form-container">
      <view class="input-group">
        <text class="label">手机号</text>
        <input 
          class="input"
          type="number"
          placeholder="请输入手机号"
          v-model="form.phone"
          maxlength="11"
        />
      </view>
      
      <view class="input-group">
        <text class="label">验证码</text>
        <view class="code-input-wrapper">
          <input 
            class="input code-input"
            type="number"
            placeholder="请输入验证码"
            v-model="form.code"
            maxlength="6"
          />
          <button 
            class="code-btn"
            :disabled="codeDisabled"
            @click="sendCode"
          >
            {{ codeText }}
          </button>
        </view>
      </view>
      
      <view class="tab-container">
        <view 
          class="tab-item"
          :class="{ active: currentTab === 'login' }"
          @click="switchTab('login')"
        >
          登录
        </view>
        <view 
          class="tab-item"
          :class="{ active: currentTab === 'register' }"
          @click="switchTab('register')"
        >
          注册
        </view>
      </view>
      
      <button 
        class="submit-btn"
        :disabled="!canSubmit"
        @click="handleSubmit"
      >
        {{ currentTab === 'login' ? '登录' : '注册' }}
      </button>
      
      <view class="agreement">
        <checkbox-group @change="onAgreementChange">
          <label class="checkbox-label">
            <checkbox value="agree" :checked="agreed" />
            <text class="agreement-text">
              我已阅读并同意
              <text class="link" @click="showAgreement">《用户协议》</text>
              和
              <text class="link" @click="showPrivacy">《隐私政策》</text>
            </text>
          </label>
        </checkbox-group>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 表单数据
const form = ref({
  phone: '',
  code: ''
})

// 当前标签页
const currentTab = ref('login')

// 验证码相关
const codeDisabled = ref(false)
const codeCountdown = ref(0)
const agreed = ref(false)

// 验证码按钮文本
const codeText = computed(() => {
  if (codeCountdown.value > 0) {
    return `${codeCountdown.value}s后重发`
  }
  return '获取验证码'
})

// 是否可以提交
const canSubmit = computed(() => {
  return form.value.phone.length === 11 && 
         form.value.code.length === 6 && 
         agreed.value
})

// 切换标签页
const switchTab = (tab) => {
  currentTab.value = tab
}

// 发送验证码
const sendCode = async () => {
  if (!form.value.phone) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none'
    })
    return
  }
  
  if (!/^1[3-9]\d{9}$/.test(form.value.phone)) {
    uni.showToast({
      title: '手机号格式不正确',
      icon: 'none'
    })
    return
  }
  
  try {
    const type = currentTab.value === 'login' ? 2 : 1
    const result = await userStore.sendSmsCode(form.value.phone, type)
    
    if (result.success) {
      uni.showToast({
        title: '验证码已发送',
        icon: 'success'
      })
      
      // 开始倒计时
      startCountdown()
    } else {
      uni.showToast({
        title: result.message || '发送失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.showToast({
      title: '网络错误',
      icon: 'none'
    })
  }
}

// 开始倒计时
const startCountdown = () => {
  codeDisabled.value = true
  codeCountdown.value = 60
  
  const timer = setInterval(() => {
    codeCountdown.value--
    if (codeCountdown.value <= 0) {
      clearInterval(timer)
      codeDisabled.value = false
    }
  }, 1000)
}

// 处理提交
const handleSubmit = async () => {
  if (!canSubmit.value) return
  
  uni.showLoading({
    title: currentTab.value === 'login' ? '登录中...' : '注册中...'
  })
  
  try {
    let result
    if (currentTab.value === 'login') {
      result = await userStore.login(form.value.phone, form.value.code)
    } else {
      result = await userStore.register(form.value.phone, form.value.code)
    }
    
    uni.hideLoading()
    
    if (result.success) {
      uni.showToast({
        title: currentTab.value === 'login' ? '登录成功' : '注册成功',
        icon: 'success'
      })
      
      // 跳转到首页
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }, 1500)
    } else {
      uni.showToast({
        title: result.message || '操作失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '网络错误',
      icon: 'none'
    })
  }
}

// 协议变化
const onAgreementChange = (e) => {
  agreed.value = e.detail.value.includes('agree')
}

// 显示用户协议
const showAgreement = () => {
  uni.showModal({
    title: '用户协议',
    content: '这里是用户协议内容...',
    showCancel: false
  })
}

// 显示隐私政策
const showPrivacy = () => {
  uni.showModal({
    title: '隐私政策',
    content: '这里是隐私政策内容...',
    showCancel: false
  })
}

onMounted(() => {
  // 检查是否已登录
  if (userStore.isLoggedIn) {
    uni.switchTab({
      url: '/pages/index/index'
    })
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80rpx 60rpx 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 100rpx;
  
  .title {
    display: block;
    font-size: 56rpx;
    font-weight: bold;
    color: white;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.form-container {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
}

.input-group {
  margin-bottom: 40rpx;
  
  .label {
    display: block;
    font-size: 28rpx;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .input {
    width: 100%;
    height: 80rpx;
    border: 2rpx solid #e0e0e0;
    border-radius: 10rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
    
    &:focus {
      border-color: #667eea;
    }
  }
}

.code-input-wrapper {
  display: flex;
  align-items: center;
  gap: 20rpx;
  
  .code-input {
    flex: 1;
  }
  
  .code-btn {
    width: 200rpx;
    height: 80rpx;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 10rpx;
    font-size: 24rpx;
    
    &:disabled {
      background: #ccc;
    }
  }
}

.tab-container {
  display: flex;
  margin-bottom: 40rpx;
  border-radius: 10rpx;
  overflow: hidden;
  border: 2rpx solid #e0e0e0;
  
  .tab-item {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 28rpx;
    background: #f5f5f5;
    color: #666;
    
    &.active {
      background: #667eea;
      color: white;
    }
  }
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  
  &:disabled {
    background: #ccc;
  }
}

.agreement {
  .checkbox-label {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #666;
    
    .agreement-text {
      margin-left: 10rpx;
      
      .link {
        color: #667eea;
      }
    }
  }
}
</style>
