package service

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	"interviewmaster/internal/config"
	"interviewmaster/internal/model"
	"interviewmaster/internal/utils"
)

// PaymentService 支付服务
type PaymentService struct {
	orderService   *OrderService
	productService *ProductService
	userService    *UserService
	config         *config.PaymentConfig
}

// NewPaymentService 创建支付服务实例
func NewPaymentService() *PaymentService {
	cfg := config.GlobalConfig
	if cfg == nil {
		panic("配置未初始化")
	}

	return &PaymentService{
		orderService:   NewOrderService(),
		productService: NewProductService(),
		userService:    NewUserService(),
		config:         &cfg.Payment,
	}
}

// CreatePaymentOrder 创建支付订单
func (s *PaymentService) CreatePaymentOrder(userID uint64, productID string) (*model.Order, error) {
	// 检查商品是否存在
	product, err := s.productService.GetProductByID(productID)
	if err != nil {
		return nil, err
	}
	if product == nil {
		return nil, fmt.Errorf("商品不存在")
	}
	if product.Status != 1 {
		return nil, fmt.Errorf("商品已下架")
	}

	// 创建订单
	order, err := s.orderService.CreateOrder(userID, productID)
	if err != nil {
		return nil, err
	}

	return order, nil
}

// WeChatPayRequest 微信支付请求
type WeChatPayRequest struct {
	OrderNo     string  `json:"order_no"`
	Amount      float64 `json:"amount"`
	Description string  `json:"description"`
	ClientIP    string  `json:"client_ip"`
}

// WeChatPayResponse 微信支付响应
type WeChatPayResponse struct {
	PrepayID  string `json:"prepay_id"`
	CodeURL   string `json:"code_url"`
	PaySign   string `json:"pay_sign"`
	TimeStamp string `json:"timestamp"`
	NonceStr  string `json:"nonce_str"`
	Package   string `json:"package"`
	SignType  string `json:"sign_type"`
}

// CreateWeChatPayment 创建微信支付
func (s *PaymentService) CreateWeChatPayment(orderNo string, clientIP string) (*WeChatPayResponse, error) {
	// 获取订单信息
	order, err := s.orderService.GetOrderByOrderNo(orderNo)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, fmt.Errorf("订单不存在")
	}
	if order.Status != model.OrderStatusPending {
		return nil, fmt.Errorf("订单状态不正确")
	}

	// 构建微信支付参数
	params := map[string]string{
		"appid":            s.config.WeChat.AppID,
		"mch_id":           s.config.WeChat.MchID,
		"nonce_str":        utils.GenerateSessionID(),
		"body":             fmt.Sprintf("面试助手-%s", order.Product.Name),
		"out_trade_no":     orderNo,
		"total_fee":        fmt.Sprintf("%.0f", order.Amount*100), // 转换为分
		"spbill_create_ip": clientIP,
		"notify_url":       s.config.WeChat.NotifyURL,
		"trade_type":       "NATIVE", // 扫码支付
	}

	// 生成签名
	sign := s.generateWeChatSign(params)
	params["sign"] = sign

	// 构建XML请求体
	xmlBody := s.buildWeChatXML(params)

	// 发送请求到微信支付API
	resp, err := http.Post("https://api.mch.weixin.qq.com/pay/unifiedorder", 
		"application/xml", strings.NewReader(xmlBody))
	if err != nil {
		return nil, fmt.Errorf("微信支付API请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 解析响应
	// 这里应该解析XML响应，为简化示例，返回模拟数据
	response := &WeChatPayResponse{
		PrepayID:  "prepay_id_" + orderNo,
		CodeURL:   "weixin://wxpay/bizpayurl?pr=mock_code",
		TimeStamp: fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:  utils.GenerateSessionID(),
		Package:   "prepay_id=" + "prepay_id_" + orderNo,
		SignType:  "MD5",
	}

	// 生成支付签名
	response.PaySign = s.generateWeChatPaySign(response)

	return response, nil
}

// AlipayRequest 支付宝支付请求
type AlipayRequest struct {
	OrderNo     string  `json:"order_no"`
	Amount      float64 `json:"amount"`
	Description string  `json:"description"`
	ReturnURL   string  `json:"return_url"`
}

// AlipayResponse 支付宝支付响应
type AlipayResponse struct {
	PayURL string `json:"pay_url"`
	QRCode string `json:"qr_code"`
}

// CreateAlipayPayment 创建支付宝支付
func (s *PaymentService) CreateAlipayPayment(orderNo string, returnURL string) (*AlipayResponse, error) {
	// 获取订单信息
	order, err := s.orderService.GetOrderByOrderNo(orderNo)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, fmt.Errorf("订单不存在")
	}
	if order.Status != model.OrderStatusPending {
		return nil, fmt.Errorf("订单状态不正确")
	}

	// 构建支付宝支付参数
	params := map[string]string{
		"app_id":      s.config.Alipay.AppID,
		"method":      "alipay.trade.precreate",
		"charset":     "utf-8",
		"sign_type":   "RSA2",
		"timestamp":   time.Now().Format("2006-01-02 15:04:05"),
		"version":     "1.0",
		"notify_url":  s.config.Alipay.NotifyURL,
		"out_trade_no": orderNo,
		"total_amount": fmt.Sprintf("%.2f", order.Amount),
		"subject":     fmt.Sprintf("面试助手-%s", order.Product.Name),
	}

	// 生成签名
	sign := s.generateAlipaySign(params)
	params["sign"] = sign

	// 构建请求URL
	payURL := s.buildAlipayURL(params)

	response := &AlipayResponse{
		PayURL: payURL,
		QRCode: payURL, // 实际应该生成二维码
	}

	return response, nil
}

// HandleWeChatNotify 处理微信支付回调
func (s *PaymentService) HandleWeChatNotify(xmlData string) error {
	// 解析XML数据
	// 这里应该解析实际的XML数据，为简化示例，假设解析成功
	
	// 验证签名
	// if !s.verifyWeChatSign(params) {
	//     return fmt.Errorf("签名验证失败")
	// }

	// 模拟解析结果
	orderNo := "IM20240101000001" // 从XML中解析
	
	// 处理支付成功
	return s.orderService.PayOrder(orderNo, "wechat")
}

// HandleAlipayNotify 处理支付宝支付回调
func (s *PaymentService) HandleAlipayNotify(params map[string]string) error {
	// 验证签名
	// if !s.verifyAlipaySign(params) {
	//     return fmt.Errorf("签名验证失败")
	// }

	orderNo := params["out_trade_no"]
	tradeStatus := params["trade_status"]

	// 检查交易状态
	if tradeStatus == "TRADE_SUCCESS" || tradeStatus == "TRADE_FINISHED" {
		return s.orderService.PayOrder(orderNo, "alipay")
	}

	return nil
}

// generateWeChatSign 生成微信支付签名
func (s *PaymentService) generateWeChatSign(params map[string]string) string {
	// 排序参数
	var keys []string
	for k := range params {
		if k != "sign" && params[k] != "" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建签名字符串
	var signStr strings.Builder
	for i, k := range keys {
		if i > 0 {
			signStr.WriteString("&")
		}
		signStr.WriteString(k)
		signStr.WriteString("=")
		signStr.WriteString(params[k])
	}
	signStr.WriteString("&key=")
	signStr.WriteString(s.config.WeChat.APIKey)

	// MD5签名
	hash := md5.Sum([]byte(signStr.String()))
	return fmt.Sprintf("%X", hash)
}

// generateWeChatPaySign 生成微信支付签名
func (s *PaymentService) generateWeChatPaySign(resp *WeChatPayResponse) string {
	params := map[string]string{
		"appId":     s.config.WeChat.AppID,
		"timeStamp": resp.TimeStamp,
		"nonceStr":  resp.NonceStr,
		"package":   resp.Package,
		"signType":  resp.SignType,
	}
	return s.generateWeChatSign(params)
}

// buildWeChatXML 构建微信支付XML
func (s *PaymentService) buildWeChatXML(params map[string]string) string {
	var xml strings.Builder
	xml.WriteString("<xml>")
	for k, v := range params {
		xml.WriteString(fmt.Sprintf("<%s><![CDATA[%s]]></%s>", k, v, k))
	}
	xml.WriteString("</xml>")
	return xml.String()
}

// generateAlipaySign 生成支付宝签名
func (s *PaymentService) generateAlipaySign(params map[string]string) string {
	// 排序参数
	var keys []string
	for k := range params {
		if k != "sign" && params[k] != "" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建签名字符串
	var signStr strings.Builder
	for i, k := range keys {
		if i > 0 {
			signStr.WriteString("&")
		}
		signStr.WriteString(k)
		signStr.WriteString("=")
		signStr.WriteString(url.QueryEscape(params[k]))
	}

	// 这里应该使用RSA2签名，为简化示例返回MD5
	hash := md5.Sum([]byte(signStr.String() + s.config.Alipay.PrivateKey))
	return fmt.Sprintf("%x", hash)
}

// buildAlipayURL 构建支付宝支付URL
func (s *PaymentService) buildAlipayURL(params map[string]string) string {
	var urlStr strings.Builder
	urlStr.WriteString("https://openapi.alipay.com/gateway.do?")
	
	for k, v := range params {
		urlStr.WriteString(k)
		urlStr.WriteString("=")
		urlStr.WriteString(url.QueryEscape(v))
		urlStr.WriteString("&")
	}
	
	return strings.TrimSuffix(urlStr.String(), "&")
}

// GetPaymentMethods 获取支付方式
func (s *PaymentService) GetPaymentMethods() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"type":        "wechat",
			"name":        "微信支付",
			"icon":        "wechat",
			"description": "使用微信扫码支付",
			"enabled":     true,
		},
		{
			"type":        "alipay",
			"name":        "支付宝",
			"icon":        "alipay",
			"description": "使用支付宝扫码支付",
			"enabled":     true,
		},
	}
}
