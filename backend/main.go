package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"

	"interviewmaster/internal/config"
	"interviewmaster/internal/handler"
	"interviewmaster/internal/middleware"
interviewmaster/internal/model
	"interviewmaster/internal/utils"
	"interviewmaster/pkg/database"
	"interviewmaster/pkg/redis"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("config.json")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("初始化MySQL失败: %v", err)
	}
	defer database.CloseMySQL()

	// 初始化Redis
	if err := redis.InitRedis(&cfg.Database.Redis); err != nil {
		log.Fatalf("初始化Redis失败: %v", err)
	}
	defer redis.CloseRedis()

	// 自动迁移数据库
	if err := model.AutoMigrate(database.GetDB()); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}

	// 初始化默认数据
	if err := model.InitDefaultData(database.GetDB()); err != nil {
		log.Fatalf("初始化默认数据失败: %v", err)
	}

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建Gin引擎
	r := gin.Default()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 创建处理器实例
	authHandler := handler.NewAuthHandler()
	userHandler := handler.NewUserHandler()

	// 基础路由
	r.GET("/", func(c *gin.Context) {
		utils.SuccessResponse(c, gin.H{
			"message": "面试官见招拆招App API服务",
			"version": "1.0.0",
			"status":  "running",
		})
	})

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		utils.SuccessResponse(c, gin.H{
			"status": "ok",
			"time":   time.Now().Unix(),
		})
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证相关路由（无需认证）
		auth := api.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.POST("/sms", authHandler.SendSMS)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/logout", authHandler.Logout)
			auth.GET("/verify", authHandler.VerifyToken)
		}

		// 用户相关路由（需要认证）
		user := api.Group("/user")
		user.Use(middleware.AuthMiddleware())
		{
			user.GET("/info", userHandler.GetUserInfo)
			user.PUT("/info", userHandler.UpdateUserInfo)
			user.GET("/balance", userHandler.GetUserBalance)
			user.POST("/consume", userHandler.ConsumeUserBalance)
		}

		// 管理员用户路由（需要管理员认证）
		adminUser := api.Group("/admin/user")
		adminUser.Use(middleware.AdminAuthMiddleware())
		{
			adminUser.GET("/list", userHandler.GetUserList)
			adminUser.PUT("/:id/balance", userHandler.UpdateUserBalance)
			adminUser.DELETE("/:id", userHandler.DeleteUser)
		}

		// 订单相关路由（需要认证）
		order := api.Group("/order")
		order.Use(middleware.AuthMiddleware())
		{
			order.POST("/create", func(c *gin.Context) {
				utils.SuccessResponseWithMessage(c, "创建订单接口", nil)
			})
			order.GET("/list", func(c *gin.Context) {
				utils.SuccessResponseWithMessage(c, "订单列表接口", nil)
			})
		}

		// 支付相关路由（需要认证）
		payment := api.Group("/payment")
		payment.Use(middleware.AuthMiddleware())
		{
			payment.POST("/wechat/pay", func(c *gin.Context) {
				utils.SuccessResponseWithMessage(c, "微信支付接口", nil)
			})
			payment.POST("/alipay/pay", func(c *gin.Context) {
				utils.SuccessResponseWithMessage(c, "支付宝支付接口", nil)
			})
		}

		// 面试历史相关路由（需要认证）
		interview := api.Group("/interview")
		interview.Use(middleware.AuthMiddleware())
		{
			interview.GET("/history", func(c *gin.Context) {
				utils.SuccessResponseWithMessage(c, "面试历史接口", nil)
			})
			interview.POST("/feedback", func(c *gin.Context) {
				utils.SuccessResponseWithMessage(c, "用户反馈接口", nil)
			})
		}
	}

	// WebSocket路由
	r.GET("/ws/interview", func(c *gin.Context) {
		utils.SuccessResponseWithMessage(c, "WebSocket面试接口", nil)
	})

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:         ":" + cfg.Server.Port,
		Handler:      r,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
	}

	// 启动服务器
	go func() {
		log.Printf("服务器启动在端口 :%s", cfg.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 优雅停机
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("正在关闭服务器...")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("服务器强制关闭:", err)
	}

	log.Println("服务器已退出")
}
