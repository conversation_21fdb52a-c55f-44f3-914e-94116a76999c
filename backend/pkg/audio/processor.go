package audio

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"interviewmaster/internal/config"
)

// AudioProcessor 音频处理器
type AudioProcessor struct {
	config *config.AudioConfig
}

// AudioFormat 音频格式
type AudioFormat struct {
	Format     string `json:"format"`
	SampleRate int    `json:"sample_rate"`
	Channels   int    `json:"channels"`
	BitDepth   int    `json:"bit_depth"`
}

// AudioMetadata 音频元数据
type AudioMetadata struct {
	Duration   float64     `json:"duration"`
	Size       int64       `json:"size"`
	Format     AudioFormat `json:"format"`
	Quality    string      `json:"quality"`
	CreatedAt  time.Time   `json:"created_at"`
}

// ProcessedAudio 处理后的音频
type ProcessedAudio struct {
	Data     []byte         `json:"data"`
	Metadata *AudioMetadata `json:"metadata"`
	Format   AudioFormat    `json:"format"`
}

// NewAudioProcessor 创建音频处理器
func NewAudioProcessor(cfg *config.AudioConfig) *AudioProcessor {
	return &AudioProcessor{
		config: cfg,
	}
}

// ValidateAudio 验证音频数据
func (p *AudioProcessor) ValidateAudio(data []byte, format string) error {
	// 检查文件大小
	if int64(len(data)) > p.config.MaxFileSize {
		return fmt.Errorf("音频文件过大，最大允许 %d 字节", p.config.MaxFileSize)
	}

	// 检查格式是否支持
	if !p.isFormatSupported(format) {
		return fmt.Errorf("不支持的音频格式: %s", format)
	}

	// 检查数据是否为空
	if len(data) == 0 {
		return fmt.Errorf("音频数据为空")
	}

	return nil
}

// isFormatSupported 检查格式是否支持
func (p *AudioProcessor) isFormatSupported(format string) bool {
	for _, supportedFormat := range p.config.AllowedFormats {
		if strings.ToLower(format) == strings.ToLower(supportedFormat) {
			return true
		}
	}
	return false
}

// ConvertAudio 转换音频格式
func (p *AudioProcessor) ConvertAudio(inputData []byte, inputFormat string) (*ProcessedAudio, error) {
	// 验证输入音频
	if err := p.ValidateAudio(inputData, inputFormat); err != nil {
		return nil, err
	}

	// 如果已经是目标格式且参数匹配，直接返回
	if strings.ToLower(inputFormat) == strings.ToLower(p.config.TargetFormat) {
		metadata := &AudioMetadata{
			Size: int64(len(inputData)),
			Format: AudioFormat{
				Format:     p.config.TargetFormat,
				SampleRate: p.config.SampleRate,
				Channels:   p.config.Channels,
				BitDepth:   p.config.BitDepth,
			},
			CreatedAt: time.Now(),
		}

		return &ProcessedAudio{
			Data:     inputData,
			Metadata: metadata,
			Format: AudioFormat{
				Format:     p.config.TargetFormat,
				SampleRate: p.config.SampleRate,
				Channels:   p.config.Channels,
				BitDepth:   p.config.BitDepth,
			},
		}, nil
	}

	// 使用FFmpeg进行转换
	return p.convertWithFFmpeg(inputData, inputFormat)
}

// convertWithFFmpeg 使用FFmpeg转换音频
func (p *AudioProcessor) convertWithFFmpeg(inputData []byte, inputFormat string) (*ProcessedAudio, error) {
	// 创建临时目录
	tempDir := os.TempDir()
	timestamp := time.Now().UnixNano()
	
	inputFile := filepath.Join(tempDir, fmt.Sprintf("input_%d.%s", timestamp, inputFormat))
	outputFile := filepath.Join(tempDir, fmt.Sprintf("output_%d.%s", timestamp, p.config.TargetFormat))
	
	// 清理临时文件
	defer func() {
		os.Remove(inputFile)
		os.Remove(outputFile)
	}()

	// 写入输入文件
	if err := os.WriteFile(inputFile, inputData, 0644); err != nil {
		return nil, fmt.Errorf("写入临时文件失败: %v", err)
	}

	// 构建FFmpeg命令
	args := []string{
		"-i", inputFile,
		"-ar", fmt.Sprintf("%d", p.config.SampleRate),
		"-ac", fmt.Sprintf("%d", p.config.Channels),
		"-sample_fmt", p.getSampleFormat(),
		"-f", p.config.TargetFormat,
		"-y", // 覆盖输出文件
		outputFile,
	}

	// 执行FFmpeg命令
	cmd := exec.Command("ffmpeg", args...)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("FFmpeg转换失败: %v, stderr: %s", err, stderr.String())
	}

	// 读取输出文件
	outputData, err := os.ReadFile(outputFile)
	if err != nil {
		return nil, fmt.Errorf("读取输出文件失败: %v", err)
	}

	// 获取音频元数据
	metadata, err := p.getAudioMetadata(outputFile)
	if err != nil {
		log.Printf("获取音频元数据失败: %v", err)
		// 使用默认元数据
		metadata = &AudioMetadata{
			Size: int64(len(outputData)),
			Format: AudioFormat{
				Format:     p.config.TargetFormat,
				SampleRate: p.config.SampleRate,
				Channels:   p.config.Channels,
				BitDepth:   p.config.BitDepth,
			},
			CreatedAt: time.Now(),
		}
	}

	return &ProcessedAudio{
		Data:     outputData,
		Metadata: metadata,
		Format: AudioFormat{
			Format:     p.config.TargetFormat,
			SampleRate: p.config.SampleRate,
			Channels:   p.config.Channels,
			BitDepth:   p.config.BitDepth,
		},
	}, nil
}

// getSampleFormat 获取采样格式
func (p *AudioProcessor) getSampleFormat() string {
	switch p.config.BitDepth {
	case 16:
		return "s16"
	case 24:
		return "s32"
	case 32:
		return "s32"
	default:
		return "s16"
	}
}

// getAudioMetadata 获取音频元数据
func (p *AudioProcessor) getAudioMetadata(filePath string) (*AudioMetadata, error) {
	// 使用ffprobe获取音频信息
	cmd := exec.Command("ffprobe",
		"-v", "quiet",
		"-print_format", "json",
		"-show_format",
		"-show_streams",
		filePath,
	)

	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("ffprobe执行失败: %v", err)
	}

	// 这里应该解析JSON输出，为简化示例，返回基本信息
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return nil, err
	}

	return &AudioMetadata{
		Size: fileInfo.Size(),
		Format: AudioFormat{
			Format:     p.config.TargetFormat,
			SampleRate: p.config.SampleRate,
			Channels:   p.config.Channels,
			BitDepth:   p.config.BitDepth,
		},
		Quality:   "high",
		CreatedAt: time.Now(),
	}, nil
}

// EncodeToBase64 将音频数据编码为Base64
func (p *AudioProcessor) EncodeToBase64(data []byte) string {
	return base64.StdEncoding.EncodeToString(data)
}

// DecodeFromBase64 从Base64解码音频数据
func (p *AudioProcessor) DecodeFromBase64(encoded string) ([]byte, error) {
	return base64.StdEncoding.DecodeString(encoded)
}

// SplitAudio 分割音频（按时长）
func (p *AudioProcessor) SplitAudio(data []byte, segmentDuration float64) ([][]byte, error) {
	// 创建临时文件
	tempDir := os.TempDir()
	timestamp := time.Now().UnixNano()
	
	inputFile := filepath.Join(tempDir, fmt.Sprintf("input_%d.%s", timestamp, p.config.TargetFormat))
	outputPattern := filepath.Join(tempDir, fmt.Sprintf("segment_%d_%%03d.%s", timestamp, p.config.TargetFormat))
	
	defer func() {
		os.Remove(inputFile)
		// 清理分割后的文件
		matches, _ := filepath.Glob(filepath.Join(tempDir, fmt.Sprintf("segment_%d_*.%s", timestamp, p.config.TargetFormat)))
		for _, match := range matches {
			os.Remove(match)
		}
	}()

	// 写入输入文件
	if err := os.WriteFile(inputFile, data, 0644); err != nil {
		return nil, fmt.Errorf("写入临时文件失败: %v", err)
	}

	// 使用FFmpeg分割音频
	cmd := exec.Command("ffmpeg",
		"-i", inputFile,
		"-f", "segment",
		"-segment_time", fmt.Sprintf("%.2f", segmentDuration),
		"-c", "copy",
		"-y",
		outputPattern,
	)

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("音频分割失败: %v", err)
	}

	// 读取分割后的文件
	matches, err := filepath.Glob(filepath.Join(tempDir, fmt.Sprintf("segment_%d_*.%s", timestamp, p.config.TargetFormat)))
	if err != nil {
		return nil, fmt.Errorf("查找分割文件失败: %v", err)
	}

	var segments [][]byte
	for _, match := range matches {
		segmentData, err := os.ReadFile(match)
		if err != nil {
			log.Printf("读取分割文件失败: %v", err)
			continue
		}
		segments = append(segments, segmentData)
	}

	return segments, nil
}

// MergeAudio 合并音频文件
func (p *AudioProcessor) MergeAudio(audioSegments [][]byte) ([]byte, error) {
	if len(audioSegments) == 0 {
		return nil, fmt.Errorf("没有音频片段可合并")
	}

	if len(audioSegments) == 1 {
		return audioSegments[0], nil
	}

	// 创建临时目录
	tempDir := os.TempDir()
	timestamp := time.Now().UnixNano()
	
	// 创建文件列表
	var inputFiles []string
	listFile := filepath.Join(tempDir, fmt.Sprintf("filelist_%d.txt", timestamp))
	outputFile := filepath.Join(tempDir, fmt.Sprintf("merged_%d.%s", timestamp, p.config.TargetFormat))
	
	defer func() {
		os.Remove(listFile)
		os.Remove(outputFile)
		for _, file := range inputFiles {
			os.Remove(file)
		}
	}()

	// 写入音频片段到临时文件
	var listContent strings.Builder
	for i, segment := range audioSegments {
		segmentFile := filepath.Join(tempDir, fmt.Sprintf("segment_%d_%d.%s", timestamp, i, p.config.TargetFormat))
		if err := os.WriteFile(segmentFile, segment, 0644); err != nil {
			return nil, fmt.Errorf("写入音频片段失败: %v", err)
		}
		inputFiles = append(inputFiles, segmentFile)
		listContent.WriteString(fmt.Sprintf("file '%s'\n", segmentFile))
	}

	// 写入文件列表
	if err := os.WriteFile(listFile, []byte(listContent.String()), 0644); err != nil {
		return nil, fmt.Errorf("写入文件列表失败: %v", err)
	}

	// 使用FFmpeg合并音频
	cmd := exec.Command("ffmpeg",
		"-f", "concat",
		"-safe", "0",
		"-i", listFile,
		"-c", "copy",
		"-y",
		outputFile,
	)

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("音频合并失败: %v", err)
	}

	// 读取合并后的文件
	mergedData, err := os.ReadFile(outputFile)
	if err != nil {
		return nil, fmt.Errorf("读取合并文件失败: %v", err)
	}

	return mergedData, nil
}
