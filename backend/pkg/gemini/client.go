package gemini

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"sync"
	"time"

	"interviewmaster/internal/config"
)

// Client Gemini API客户端
type Client struct {
	apiKey     string
	baseURL    string
	model      string
	httpClient *http.Client
	sessions   map[string]*Session
	mutex      sync.RWMutex
}

// Session Gemini会话
type Session struct {
	ID          string                 `json:"id"`
	UserID      uint64                 `json:"user_id"`
	Model       string                 `json:"model"`
	Config      *SessionConfig         `json:"config"`
	Messages    []Message              `json:"messages"`
	Context     map[string]interface{} `json:"context"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	IsActive    bool                   `json:"is_active"`
	mutex       sync.RWMutex           `json:"-"`
}

// SessionConfig 会话配置
type SessionConfig struct {
	Temperature      float64  `json:"temperature"`
	TopP             float64  `json:"top_p"`
	TopK             int      `json:"top_k"`
	MaxOutputTokens  int      `json:"max_output_tokens"`
	StopSequences    []string `json:"stop_sequences"`
	SafetySettings   []SafetySetting `json:"safety_settings"`
	SystemPrompt     string   `json:"system_prompt"`
	InterviewDomain  string   `json:"interview_domain"`
	PromptVersion    string   `json:"prompt_version"`
}

// Message 消息结构
type Message struct {
	Role      string                 `json:"role"`
	Content   []Content              `json:"content"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// Content 内容结构
type Content struct {
	Type string      `json:"type"`
	Data interface{} `json:"data"`
}

// SafetySetting 安全设置
type SafetySetting struct {
	Category  string `json:"category"`
	Threshold string `json:"threshold"`
}

// AudioData 音频数据
type AudioData struct {
	Format     string `json:"format"`
	SampleRate int    `json:"sample_rate"`
	Channels   int    `json:"channels"`
	Data       []byte `json:"data"`
}

// NewClient 创建新的Gemini客户端
func NewClient(cfg *config.GeminiConfig) *Client {
	return &Client{
		apiKey:  cfg.APIKey,
		baseURL: cfg.BaseURL,
		model:   cfg.Model,
		httpClient: &http.Client{
			Timeout: cfg.Timeout,
		},
		sessions: make(map[string]*Session),
	}
}

// CreateSession 创建新会话
func (c *Client) CreateSession(userID uint64, domain, promptVersion string) (*Session, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	sessionID := fmt.Sprintf("session_%d_%d", userID, time.Now().UnixNano())
	
	// 获取系统提示词
	systemPrompt := c.getSystemPrompt(domain, promptVersion)
	
	session := &Session{
		ID:     sessionID,
		UserID: userID,
		Model:  c.model,
		Config: &SessionConfig{
			Temperature:     0.7,
			TopP:           0.8,
			TopK:           40,
			MaxOutputTokens: 2048,
			SystemPrompt:    systemPrompt,
			InterviewDomain: domain,
			PromptVersion:   promptVersion,
			SafetySettings: []SafetySetting{
				{Category: "HARM_CATEGORY_HARASSMENT", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
				{Category: "HARM_CATEGORY_HATE_SPEECH", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
				{Category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
				{Category: "HARM_CATEGORY_DANGEROUS_CONTENT", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
			},
		},
		Messages:  make([]Message, 0),
		Context:   make(map[string]interface{}),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		IsActive:  true,
	}

	c.sessions[sessionID] = session
	log.Printf("创建Gemini会话: %s, 用户: %d, 领域: %s", sessionID, userID, domain)
	
	return session, nil
}

// GetSession 获取会话
func (c *Client) GetSession(sessionID string) *Session {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.sessions[sessionID]
}

// CloseSession 关闭会话
func (c *Client) CloseSession(sessionID string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	if session, exists := c.sessions[sessionID]; exists {
		session.IsActive = false
		delete(c.sessions, sessionID)
		log.Printf("关闭Gemini会话: %s", sessionID)
	}
}

// SendAudio 发送音频数据
func (c *Client) SendAudio(sessionID string, audioData *AudioData) (*Message, error) {
	session := c.GetSession(sessionID)
	if session == nil {
		return nil, fmt.Errorf("会话不存在: %s", sessionID)
	}

	session.mutex.Lock()
	defer session.mutex.Unlock()

	// 构建请求消息
	message := Message{
		Role: "user",
		Content: []Content{
			{
				Type: "audio",
				Data: audioData,
			},
		},
		Timestamp: time.Now(),
	}

	// 添加到会话历史
	session.Messages = append(session.Messages, message)
	session.UpdatedAt = time.Now()

	// 调用Gemini API
	response, err := c.callGeminiAPI(session, message)
	if err != nil {
		return nil, err
	}

	// 添加响应到会话历史
	session.Messages = append(session.Messages, *response)
	session.UpdatedAt = time.Now()

	return response, nil
}

// SendText 发送文本消息
func (c *Client) SendText(sessionID string, text string) (*Message, error) {
	session := c.GetSession(sessionID)
	if session == nil {
		return nil, fmt.Errorf("会话不存在: %s", sessionID)
	}

	session.mutex.Lock()
	defer session.mutex.Unlock()

	// 构建请求消息
	message := Message{
		Role: "user",
		Content: []Content{
			{
				Type: "text",
				Data: text,
			},
		},
		Timestamp: time.Now(),
	}

	// 添加到会话历史
	session.Messages = append(session.Messages, message)
	session.UpdatedAt = time.Now()

	// 调用Gemini API
	response, err := c.callGeminiAPI(session, message)
	if err != nil {
		return nil, err
	}

	// 添加响应到会话历史
	session.Messages = append(session.Messages, *response)
	session.UpdatedAt = time.Now()

	return response, nil
}

// callGeminiAPI 调用Gemini API
func (c *Client) callGeminiAPI(session *Session, message Message) (*Message, error) {
	// 构建API请求
	requestBody := map[string]interface{}{
		"model": session.Model,
		"messages": []map[string]interface{}{
			{
				"role":    "system",
				"content": session.Config.SystemPrompt,
			},
		},
		"generationConfig": map[string]interface{}{
			"temperature":     session.Config.Temperature,
			"topP":           session.Config.TopP,
			"topK":           session.Config.TopK,
			"maxOutputTokens": session.Config.MaxOutputTokens,
		},
		"safetySettings": session.Config.SafetySettings,
	}

	// 添加历史消息
	messages := requestBody["messages"].([]map[string]interface{})
	for _, msg := range session.Messages {
		apiMessage := map[string]interface{}{
			"role": msg.Role,
		}
		
		// 处理不同类型的内容
		if len(msg.Content) > 0 {
			content := msg.Content[0]
			switch content.Type {
			case "text":
				apiMessage["content"] = content.Data
			case "audio":
				// 对于音频，这里需要根据Gemini API的实际格式调整
				apiMessage["content"] = "用户发送了音频消息"
			}
		}
		
		messages = append(messages, apiMessage)
	}
	requestBody["messages"] = messages

	// 序列化请求体
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 创建HTTP请求
	url := fmt.Sprintf("%s/v1/models/%s:generateContent", c.baseURL, session.Model)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.apiKey)

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败: %d, %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var apiResponse map[string]interface{}
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 提取回答内容
	responseText := "抱歉，我无法理解您的问题。"
	if candidates, ok := apiResponse["candidates"].([]interface{}); ok && len(candidates) > 0 {
		if candidate, ok := candidates[0].(map[string]interface{}); ok {
			if content, ok := candidate["content"].(map[string]interface{}); ok {
				if parts, ok := content["parts"].([]interface{}); ok && len(parts) > 0 {
					if part, ok := parts[0].(map[string]interface{}); ok {
						if text, ok := part["text"].(string); ok {
							responseText = text
						}
					}
				}
			}
		}
	}

	// 构建响应消息
	response := &Message{
		Role: "assistant",
		Content: []Content{
			{
				Type: "text",
				Data: responseText,
			},
		},
		Timestamp: time.Now(),
		Metadata: map[string]interface{}{
			"model":           session.Model,
			"prompt_version":  session.Config.PromptVersion,
			"interview_domain": session.Config.InterviewDomain,
		},
	}

	return response, nil
}

// getSystemPrompt 获取系统提示词
func (c *Client) getSystemPrompt(domain, version string) string {
	// 基础提示词
	basePrompt := `你是一个专业的面试助手，专门帮助求职者回答技术面试问题。请遵循以下原则：

1. 提供准确、专业的技术回答
2. 回答要简洁明了，重点突出
3. 适当举例说明复杂概念
4. 保持积极正面的语调
5. 如果不确定答案，诚实说明并提供相关思路

请根据面试官的问题，提供最佳的回答建议。`

	// 根据领域添加专业提示词
	domainPrompts := map[string]string{
		"frontend": "\n\n你专精于前端开发，包括HTML、CSS、JavaScript、React、Vue、Angular等技术栈。",
		"backend":  "\n\n你专精于后端开发，包括Java、Python、Go、Node.js、数据库、微服务等技术栈。",
		"fullstack": "\n\n你专精于全栈开发，熟悉前端和后端技术，以及系统架构设计。",
		"mobile":   "\n\n你专精于移动端开发，包括iOS、Android、React Native、Flutter等技术栈。",
		"devops":   "\n\n你专精于DevOps和运维，包括Docker、Kubernetes、CI/CD、云服务等技术栈。",
	}

	if domainPrompt, exists := domainPrompts[domain]; exists {
		basePrompt += domainPrompt
	}

	// 根据版本进行A/B测试调整
	if version == "B" {
		basePrompt += "\n\n请在回答中更多地结合实际项目经验和最佳实践。"
	}

	return basePrompt
}

// GetSessionStats 获取会话统计
func (c *Client) GetSessionStats() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	activeCount := 0
	for _, session := range c.sessions {
		if session.IsActive {
			activeCount++
		}
	}

	return map[string]interface{}{
		"total_sessions":  len(c.sessions),
		"active_sessions": activeCount,
	}
}
